const functions = require("firebase-functions");
const axios = require("axios");
// var pdf2table = require("pdf2table");
var PDFParser = require("pdf2json/pdfparser");
const { initializeApp } = require("firebase-admin/app");
const {
  getFirestore,
  FieldValue,
  Timestamp,
} = require("firebase-admin/firestore");
const { getAuth, updateUser } = require("firebase-admin/auth");
const { getStorage } = require("firebase-admin/storage");
const { addMonths, endOfDay, endOfYear, setHours, setMinutes, setSeconds } = require("date-fns");
// const { CookieJar } = require("./node_modules/tough-cookie/dist/cookie/cookieJar");
// const { wrapper } = require("./node_modules/axios-cookiejar-support/dist/index");
// const { wrapper: axiosCookieJarSupport } = require('axios-cookiejar-support'); // error in func emul
// const { wrapper } = require('axios-cookiejar-support');  //  error in func emul
const toughCookie = require('tough-cookie');


let storageBucket = "transact-a4e5e.appspot.com";
if (
  process.env.FUNCTIONS_ENVIRONMENT &&
  (process.env.FUNCTIONS_ENVIRONMENT === "staging" ||
    process.env.FUNCTIONS_ENVIRONMENT === "production")
) {
  storageBucket = "transact-staging.appspot.com";
}
if (
  process.env.FUNCTIONS_ENVIRONMENT &&
  process.env.FUNCTIONS_ENVIRONMENT === "testing"
) {
  storageBucket = "transact-testing.appspot.com";
}
console.log("STORAGE BUCKET: ", storageBucket);
const app = initializeApp({
  storageBucket: storageBucket,
});
const db = getFirestore(app);
const auth = getAuth(app);



//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////  ADMIN CREATE NEW USER  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.createNewUser = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    getAuth()
      .createUser({
        email: data.email,
        password: `Transactioner${data.lastName}`,
      })
      .then(async (userRecord) => {
        console.log("Successfully created new user:", userRecord.uid);
        const userData = data;
        userData.id = userRecord.uid;
        userData.userId = userRecord.uid;
        userData.type = "user";
        if (data.hasBrokerageLogo && data.brokerageLogoRef) {
          userData.brokerageLogoRef = `https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${data.brokerageLogoRef}/logo.png`;
        }
        userData.hasBrokerageLogo =
          data.hasBrokerageLogo === "true" ? true : false;

        // Handle free trial duration (default to 3 months if not specified)
        const freeTrialDuration = data.freeTrialDurationMonths || 3;
        const datePayment = new Date();

        // Calculate dateExpires based on the selected duration
        let dateExpires;
        if (freeTrialDuration === "endOfYear") {
          // Set to December 31 of current year at 11:59:59 PM
          dateExpires = endOfYear(new Date());
          dateExpires = setHours(dateExpires, 23);
          dateExpires = setMinutes(dateExpires, 59);
          dateExpires = setSeconds(dateExpires, 59);
        } else {
          // Set dateExpires to 11:59:59 PM for month-based durations
          dateExpires = endOfDay(addMonths(new Date(), freeTrialDuration));
          dateExpires = setHours(dateExpires, 23);
          dateExpires = setMinutes(dateExpires, 59);
          dateExpires = setSeconds(dateExpires, 59);
        }

        userData.payments = [];
        userData.payments[0] = {
          amount: "0 Free Trial",
          datePayment: datePayment,
          dateExpires: dateExpires,
        };

        // Add additionalFormsAccess array
        userData.additionalFormsAccess = data.additionalFormsAccess || [];

        // Handle manager selection
        if (data.managerId && data.managerId !== "") {
          userData.managerId = data.managerId;

          // Fetch manager details from the users collection
          try {
            const managerDoc = await db.collection("users").doc(data.managerId).get();
            if (managerDoc.exists) {
              const managerData = managerDoc.data();
              userData.managerDetails = {
                firstName: managerData.firstName || "",
                lastName: managerData.lastName || "",
                managerEmail: managerData.email || "",
                sendEmailNotificationsOfNewTransactions: managerData.sendEmailNotificationsOfNewTransactions || false
              };
            }
          } catch (error) {
            console.log("Error fetching manager details:", error);
          }
        }

        // Add notes and referral fields
        userData.notes = data.notes || "";
        userData.referral = data.referral || "";

        // Auto-create emailSigField
        const emailSigParts = [
          "",
          "--\n",
         (data.firstName || "") + " " + (data.lastName || ""),
          data.brokerageName || "",
          data.phone || ""
        ].filter(part => part !== ""); // Remove empty parts except the initial blank line

        // Ensure we start with a blank line, then add the rest
        userData.emailSigField = "\n" + emailSigParts.slice(1).join("\n");

        // Set default formTopLogo settings to true
        userData.formTopLogo = {
          showAgentEmail: true,
          showAgentName: true,
          showAgentPhone: true,
          ...(data.formTopLogo || {}) // Allow override if provided
        };

        userData.createdAt = FieldValue.serverTimestamp();
        await db.collection("users").doc(userRecord.uid).set(userData);
        resolve(userRecord.uid);
      })
      .catch((error) => {
        console.log("Error creating new user:", error);
        reject("Error creating new user:", error);
      });
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////  BULK CREATE USERS FROM CSV  ////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.createUsersFromCsv = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    try {
      // Verify admin authentication
      if (!context.auth) {
        reject(new Error("Authentication required"));
        return;
      }

      // Check if user is admin (you may need to adjust this based on your auth structure)
      const userDoc = await db.collection("users").doc(context.auth.uid).get();
      if (!userDoc.exists || !userDoc.data().isAdmin) {
        reject(new Error("Admin privileges required"));
        return;
      }

      const { users } = data;
      if (!users || !Array.isArray(users) || users.length === 0) {
        reject(new Error("No users data provided"));
        return;
      }

      const results = [];
      const errors = [];

      // Process each user
      for (let i = 0; i < users.length; i++) {
        const userData = users[i];

        try {
          // Validate required fields
          if (!userData.email || !userData.firstName || !userData.lastName) {
            throw new Error("Missing required fields: email, firstName, or lastName");
          }

          // Check if user already exists
          try {
            await getAuth().getUserByEmail(userData.email);
            throw new Error(`User with email ${userData.email} already exists`);
          } catch (authError) {
            // If user doesn't exist, continue with creation
            if (authError.code !== 'auth/user-not-found') {
              throw authError;
            }
          }

          // Create user in Firebase Auth
          const userRecord = await getAuth().createUser({
            email: userData.email,
            password: `Transactioner${userData.lastName}`,
          });

          console.log("Successfully created new user:", userRecord.uid);

          // Prepare user data for Firestore
          const firestoreUserData = {
            ...userData,
            id: userRecord.uid,
            userId: userRecord.uid,
            type: "user",
            createdAt: FieldValue.serverTimestamp(),
          };

          // Handle brokerage logo
          if (userData.hasBrokerageLogo && userData.brokerageLogoRef) {
            firestoreUserData.brokerageLogoRef = `https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${userData.brokerageLogoRef}/logo.png`;
          }
          firestoreUserData.hasBrokerageLogo = userData.hasBrokerageLogo === "true" ? true : false;

          // Handle free trial duration
          const freeTrialDuration = userData.freeTrialDurationMonths || 3;
          const datePayment = new Date();
          let dateExpires;

          if (freeTrialDuration === "endOfYear") {
            dateExpires = endOfYear(new Date());
            dateExpires = setHours(dateExpires, 23);
            dateExpires = setMinutes(dateExpires, 59);
            dateExpires = setSeconds(dateExpires, 59);
          } else {
            dateExpires = endOfDay(addMonths(new Date(), freeTrialDuration));
            dateExpires = setHours(dateExpires, 23);
            dateExpires = setMinutes(dateExpires, 59);
            dateExpires = setSeconds(dateExpires, 59);
          }

          // Handle payments from CSV or default free trial
          if (userData.payments && Array.isArray(userData.payments) && userData.payments.length > 0) {
            firestoreUserData.payments = userData.payments;
          } else {
            firestoreUserData.payments = [{
              amount: "0 Free Trial",
              datePayment: datePayment,
              dateExpires: dateExpires,
            }];
          }

          // Handle additional forms access
          firestoreUserData.additionalFormsAccess = userData.additionalFormsAccess || [];

          // Handle manager selection
          if (userData.managerId && userData.managerId !== "") {
            firestoreUserData.managerId = userData.managerId;
            try {
              const managerDoc = await db.collection("users").doc(userData.managerId).get();
              if (managerDoc.exists) {
                const managerData = managerDoc.data();
                firestoreUserData.managerDetails = {
                  firstName: managerData.firstName || "",
                  lastName: managerData.lastName || "",
                  managerEmail: managerData.email || "",
                  sendEmailNotificationsOfNewTransactions: managerData.sendEmailNotificationsOfNewTransactions || false
                };
              }
            } catch (error) {
              console.log("Error fetching manager details:", error);
            }
          }

          // Auto-create email signature
          const emailSigParts = [
            "",
            "--\n",
            (userData.firstName || "") + " " + (userData.lastName || ""),
            userData.brokerageName || "",
            userData.phone || ""
          ].filter(part => part !== "");

          firestoreUserData.emailSigField = "\n" + emailSigParts.slice(1).join("\n");

          // Set default form top logo settings
          firestoreUserData.formTopLogo = {
            showAgentEmail: true,
            showAgentName: true,
            showAgentPhone: true,
            ...(userData.formTopLogo || {})
          };

          // Save to Firestore
          await db.collection("users").doc(userRecord.uid).set(firestoreUserData);

          results.push({
            index: i,
            email: userData.email,
            uid: userRecord.uid,
            status: 'success'
          });

        } catch (error) {
          console.log(`Error creating user at index ${i}:`, error);
          errors.push({
            index: i,
            email: userData.email || 'N/A',
            error: error.message
          });
        }
      }

      resolve({
        success: true,
        totalProcessed: users.length,
        successCount: results.length,
        errorCount: errors.length,
        results: results,
        errors: errors
      });

    } catch (error) {
      console.log("Error in bulk user creation:", error);
      reject(error);
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/////////////  GET DEADLINES FROM PDF EXTRA FUNCTIONS  ///////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

function parse(pdfBuffer, callback) {
  var pdfParser = new PDFParser();

  // adding try/catch/printstack 'cause pdfParser seems to prevent errors from bubbing up (weird implementation).
  // It also doesn't seem to implement the callback(err, otherdata) convention used in most Node.js modules, so let's fix that here.
  pdfParser.on("pdfParser_dataReady", function (data) {
    try {
      pdfParserCallback(null, data);
    } catch (err) {
      console.log(err.stack);
    }
  });

  pdfParser.on("pdfParser_dataError", function (err) {
    try {
      pdfParserCallback(err, null);
    } catch (err) {
      console.log(err.stack);
    }
  });

  function pdfParserCallback(err, data) {
    if (err) return callback(err);

    // PDF's contain pages and each page contains Texts. These texts have an x and y value.
    // So finding Texts with equal y values seems like the solution.
    // However, some y values are off by 0.010 pixels/points so let's first find what the smallest y value could be.

    // Let's find Texts with the same x value and look for the smallest y distance of these Texts (on the same page of course)
    // Then use those smallest y values (per page) to find Texts that seem to be on the same row
    // If no smallest y value (per page) can be found, use 0 as smallest distance.

    // Let's get started:

    // find smallest y value between 2 texts with equal x values:
    var smallestYValueForPage = [];

    for (var p = 0; p < data.Pages.length; p++) {
      var page = data.Pages[p];

      var smallestYValue = null; // per page

      var textsWithSameXvalues = {};

      for (var t = 0; t < page.Texts.length; t++) {
        var text = page.Texts[t];

        if (!textsWithSameXvalues[text.x]) {
          textsWithSameXvalues[text.x] = [];
        }
        textsWithSameXvalues[text.x].push(text);
      }

      // find smallest y distance:
      for (var x in textsWithSameXvalues) {
        var texts = textsWithSameXvalues[x];

        for (var i = 0; i < texts.length; i++) {
          var firstYvalue = texts[i].y;

          for (var j = 0; j < texts.length; j++) {
            if (texts[i] !== texts[j]) {
              var distance = Math.abs(texts[j].y - texts[i].y);
              if (smallestYValue === null || distance < smallestYValue) {
                smallestYValue = distance;
              }
            }
          }
        }
      }

      if (smallestYValue === null) smallestYValue = 0;
      smallestYValueForPage.push(smallestYValue);
    }

    // now lets find Texts with 'the same' y-values, Actually y-values in the range of y-smallestYValue and y+smallestYValue:
    var myPages = [];

    for (var p = 0; p < data.Pages.length; p++) {
      var page = data.Pages[p];

      var rows = []; // store Texts and their x positions in rows

      for (var t = 0; t < page.Texts.length; t++) {
        var text = page.Texts[t];

        var foundRow = false;
        for (var r = rows.length - 1; r >= 0; r--) {
          // y value of Text falls within the y-value range, add text to row:
          var maxYdifference = smallestYValueForPage[p];
          if (
            rows[r].y - maxYdifference < text.y &&
            text.y < rows[r].y + maxYdifference
          ) {
            // only add value of T to data (which is the actual text):
            for (var i = 0; i < text.R.length; i++) {
              rows[r].data.push({
                text: decodeURIComponent(text.R[i].T),
                x: text.x,
              });
            }
            foundRow = true;
          }
        }
        if (!foundRow) {
          // create new row:
          var row = {
            y: text.y,
            data: [],
          };

          // add text to row:
          for (var i = 0; i < text.R.length; i++) {
            row.data.push({
              text: decodeURIComponent(text.R[i].T),
              x: text.x,
            });
          }

          // add row to rows:
          rows.push(row);
        }
      }

      // sort each extracted row
      for (var i = 0; i < rows.length; i++) {
        rows[i].data.sort(comparer);
      }

      // add rows to pages:
      myPages.push(rows);
    }

    // flatten pages into rows:
    var rows = [];

    for (var p = 0; p < myPages.length; p++) {
      for (var r = 0; r < myPages[p].length; r++) {
        // now that each row is made of objects
        // we need to extract the 'text' property from the object
        var rowEntries = [];
        var row = myPages[p][r].data;
        for (var i = 0; i < row.length; i++) {
          rowEntries.push(row[i].text);
        }
        // now append the extracted and ordered text into the return rows.
        rows.push(rowEntries);
      }
    }

    // return callback:
    callback(null, rows, myPages);
  }

  pdfParser.parseBuffer(pdfBuffer);
}

var comparer = function (a, b) {
  if (a.x > b.x) {
    return 1;
  }
  if (a.x < b.x) {
    return -1;
  }
  // a must be equal to b
  return 0;
};

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////////  GET DEADLINES FROM PDF  ////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.getDeadlinesFromPdf = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    const sourceFile = await getStorage().bucket().file(data).download();
    if (sourceFile && sourceFile[0]) {
      // pdf2table.parse(sourceFile[0], function (err, rows, rowsdebug) {
      parse(sourceFile[0], function (err, rows, rowsdebug) {
        if (err) return resolve({ error: err });
        resolve(rows);
      });
    } else {
      resolve({ error: "Problem with source file" });
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////  LINK USER TO PARTY  //////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.linkUserToParty = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    if (
      data.party.hasTransactionCoordinator &&
      data.party.transactionCoordinator?.email
    ) {
      auth
        .getUserByEmail(data.party.transactionCoordinator.email)
        .then(async (user) => {
          console.log("FOUND TC USER: ", user.email);
          const userProfile = await db.collection("users").doc(user.uid).get();
          if (!userProfile.exists) {
            console.log("NO TC USER FOUND");
          } else if (userProfile.data() && userProfile.data().type !== "user") {
            console.log("TC IS NOT USER");
          } else {
            const partyRef = db.collection("parties").doc(data.partyId);
            await partyRef.update({
              [`transactionCoordinator.isUser`]: true,
            });
          }
        })
        .catch((error) => {
          console.log("NO TC USER FOUND");
        });
    }
    if (data.party.hasCoAgent && data.party.coAgent?.email) {
      auth
        .getUserByEmail(data.party.coAgent.email)
        .then(async (user) => {
          console.log("FOUND COAGENT USER: ", user.email);
          const userProfile = await db.collection("users").doc(user.uid).get();
          if (!userProfile.exists) {
            console.log("NO COAGENT USER FOUND");
          } else if (userProfile.data() && userProfile.data().type !== "user") {
            console.log("COAGENT IS NOT USER");
          } else {
            const partyRef = db.collection("parties").doc(data.partyId);
            await partyRef.update({
              [`coAgent.isUser`]: true,
            });
          }
        })
        .catch((error) => {
          console.log("NO COAGENT USER FOUND");
        });
    }
    auth
      .getUserByEmail(data.party.email)
      .then(async (user) => {
        console.log("FOUND USER: ", user.email);
        console.log("PARTY ID: ", data.partyId);
        const userProfile = await db.collection("users").doc(user.uid).get();
        if (!userProfile.exists) {
          console.log("NO USER FOUND");
          resolve({ error: "No user found" });
        } else if (userProfile.data() && userProfile.data().type !== "user") {
          console.log("PERSON IS NOT USER");
          resolve({ error: "Person is not user" });
        } else {
          const partyRef = db.collection("parties").doc(data.partyId);
          await partyRef.update({
            isUser: true,
            linkedUserId: user.uid,
          });
          resolve({ error: "" });
        }
      })
      .catch((error) => {
        console.log("NO USER FOUND");
        resolve({ error: "No user found" });
      });
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////  LINK TCID TO PARTY  //////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.linkTcUserToParty = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    auth
      .getUserByEmail(data.email)
      .then(async (user) => {
        console.log("FOUND USER: ", user.email);
        console.log("PARTY ID: ", data.partyId);
        const userProfile = await db.collection("users").doc(user.uid).get();
        if (!userProfile.exists) {
          console.log("NO USER FOUND");
          resolve({ error: "No user found" });
        } else if (userProfile.data() && userProfile.data().type !== "user") {
          console.log("PERSON IS NOT USER");
          resolve({ error: "Person is not user" });
        } else {
          const partyRef = db.collection("parties").doc(data.partyId);
          await partyRef.update({
            isTcUser: true,
            linkedTcUserId: user.uid,
          });
          resolve({ error: "" });
        }
      })
      .catch((error) => {
        console.log("NO USER FOUND");
        resolve({ error: "No user found" });
      });
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////  ADD TCID TO DOCS, TASKS, AND PARTIES  //////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function addAgentAsTcIdToDocsTasksParties(transactionId, agentAsTcId) {
  const tasksRef = db.collection("tasks");
  const snapshotTask = await tasksRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotTask.empty) {
    console.log("No matching documents.");
  } else {
    snapshotTask.forEach((doc) => {
      const taskRef = db.collection("tasks").doc(doc.id);
      const res = taskRef.update({ agentAsTcId: agentAsTcId });
    });
  }
  const docsRef = db.collection("documents");
  const snapshotDocs = await docsRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotDocs.empty) {
    console.log("No matching documents.");
  } else {
    snapshotDocs.forEach((doc) => {
      const docRef = db.collection("documents").doc(doc.id);
      const res = docRef.update({ agentAsTcId: agentAsTcId });
    });
  }
  const partiesRef = db.collection("parties");
  const snapshotParties = await partiesRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotParties.empty) {
    console.log("No matching documents.");
  } else {
    snapshotParties.forEach((doc) => {
      const partyRef = db.collection("parties").doc(doc.id);
      const res = partyRef.update({ agentAsTcId: agentAsTcId });
    });
  }
  return;
}

async function addTcIdToDocsTasksParties(transactionId, tcId) {
  const tasksRef = db.collection("tasks");
  const snapshotTask = await tasksRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotTask.empty) {
    console.log("No matching tasks.");
  } else {
    const taskUpdatePromises = [];
    snapshotTask.forEach((doc) => {
      const taskRef = db.collection("tasks").doc(doc.id);
      taskUpdatePromises.push(taskRef.update({ tcId: tcId }));
    });
    await Promise.all(taskUpdatePromises);
    console.log(`Updated ${taskUpdatePromises.length} tasks with tcId: ${tcId}`);
  }

  const docsRef = db.collection("documents");
  const snapshotDocs = await docsRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotDocs.empty) {
    console.log("No matching documents.");
  } else {
    const docUpdatePromises = [];
    snapshotDocs.forEach((doc) => {
      const docRef = db.collection("documents").doc(doc.id);
      docUpdatePromises.push(docRef.update({ tcId: tcId }));
    });
    await Promise.all(docUpdatePromises);
    console.log(`Updated ${docUpdatePromises.length} documents with tcId: ${tcId}`);
  }

  const partiesRef = db.collection("parties");
  const snapshotParties = await partiesRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotParties.empty) {
    console.log("No matching parties.");
  } else {
    const partyUpdatePromises = [];
    snapshotParties.forEach((doc) => {
      const partyRef = db.collection("parties").doc(doc.id);
      partyUpdatePromises.push(partyRef.update({ tcId: tcId }));
    });
    await Promise.all(partyUpdatePromises);
    console.log(`Updated ${partyUpdatePromises.length} parties with tcId: ${tcId}`);
  }

  // console.log(`Successfully added tcId ${tcId} to all documents, tasks, and parties for transaction ${transactionId}`);
  return;
}

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////////  ADD TCID TO TRANSACTION  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.addAgentAsTcToTransaction = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    const party = data.party;
    // See if user already exists on our site and has role=agent, type=user
    auth
      .getUserByEmail(party.email)
      .then(async (userRecord) => {
        // Check if user has role=agent and type=user
        const userDoc = await db.collection("users").doc(userRecord.uid).get();
        const userData = userDoc.data();

        if (userData && userData.role === "agent" && userData.type === "user") {
          const transactionRef = db
            .collection("transactions")
            .doc(party.transactionId);
          await transactionRef.update({
            agentAsTcId: userRecord.uid,
            hasAgentAsTc: true,
            agentAsTransactionCoordinator: {
              email: party.email || "",
              firstName: party.firstName || "",
              lastName: party.lastName || "",
              userId: userRecord.uid,
            },
          });
          await addAgentAsTcIdToDocsTasksParties(party.transactionId, userRecord.uid);
          resolve(userRecord.uid);
        } else {
          reject(new Error("User must have role=agent and type=user to act as TC"));
        }
      })
      .catch((error) => {
        reject(new Error("User not found or not eligible to act as TC"));
      });
  });
});

exports.addTcIdToTransaction = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    const party = data.party;
    // See if user already exists on our site
    auth
      .getUserByEmail(party.email)
      .then(async (userRecord) => {
        const transactionRef = db
          .collection("transactions")
          .doc(party.transactionId);
        await transactionRef.update({
          tcId: userRecord.uid,
          hasTc: true,
          transactionCoordinator: {
            email: party.email || "",
            firstName: party.firstName || "",
            lastName: party.lastName || "",
            userId: userRecord.uid,
          },
        });
        await addTcIdToDocsTasksParties(party.transactionId, userRecord.uid);
        resolve(userRecord.uid);
      })
      .catch((error) => {
        console.log("CREATING NEW USER");
        getAuth()
          .createUser({
            email: party.email,
            password: party.transactionId,
          })
          .then(async (userRecord) => {
            console.log("Successfully created new user:", userRecord.uid);
            const transactionRef = db
              .collection("transactions")
              .doc(party.transactionId);
            await transactionRef.update({
              tcId: userRecord.uid,
              hasTc: true,
              transactionCoordinator: {
                email: party.email || "",
                firstName: party.firstName || "",
                lastName: party.lastName || "",
                userId: userRecord.uid,
              },
            });
            await db
              .collection("users")
              .doc(userRecord.uid)
              .set({
                firstName: party.firstName || "First",
                lastName: party.lastName || "Last",
                email: party.email,
                id: userRecord.uid,
                userId: userRecord.uid,
                role: "tc",
                type: "user",
                createdAt: FieldValue.serverTimestamp(),
              });
            await addTcIdToDocsTasksParties(
              party.transactionId,
              userRecord.uid
            );
            resolve(userRecord.uid);
          })
          .catch((error) => {
            console.log("Error creating new user:", error);
            reject("Error creating new user:", error);
          });
      });
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////  REMOVE TCID FROM DOCS, TASKS, AND PARTIES  //////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function removeAgentAsTcFromDocsTasksParties(transactionId) {
  const tasksRef = db.collection("tasks");
  const snapshotTask = await tasksRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotTask.empty) {
    console.log("No matching documents.");
  } else {
    snapshotTask.forEach((doc) => {
      const taskRef = db.collection("tasks").doc(doc.id);
      const res = taskRef.update({ agentAsTcId: null });
    });
  }
  const docsRef = db.collection("documents");
  const snapshotDocs = await docsRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotDocs.empty) {
    console.log("No matching documents.");
  } else {
    snapshotDocs.forEach((doc) => {
      const docRef = db.collection("documents").doc(doc.id);
      const res = docRef.update({ agentAsTcId: null });
    });
  }
  const partiesRef = db.collection("parties");
  const snapshotParties = await partiesRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotParties.empty) {
    console.log("No matching documents.");
  } else {
    snapshotParties.forEach((doc) => {
      const partyRef = db.collection("parties").doc(doc.id);
      const res = partyRef.update({ agentAsTcId: null });
    });
  }
  return;
}

async function removeTcIdToDocsTasksParties(transactionId) {
  const tasksRef = db.collection("tasks");
  const snapshotTask = await tasksRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotTask.empty) {
    console.log("No matching tasks.");
  } else {
    const taskUpdatePromises = [];
    snapshotTask.forEach((doc) => {
      const taskRef = db.collection("tasks").doc(doc.id);
      taskUpdatePromises.push(taskRef.update({ tcId: null }));
    });
    await Promise.all(taskUpdatePromises);
    console.log(`Removed tcId from ${taskUpdatePromises.length} tasks`);
  }

  const docsRef = db.collection("documents");
  const snapshotDocs = await docsRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotDocs.empty) {
    console.log("No matching documents.");
  } else {
    const docUpdatePromises = [];
    snapshotDocs.forEach((doc) => {
      const docRef = db.collection("documents").doc(doc.id);
      docUpdatePromises.push(docRef.update({ tcId: null }));
    });
    await Promise.all(docUpdatePromises);
    console.log(`Removed tcId from ${docUpdatePromises.length} documents`);
  }

  const partiesRef = db.collection("parties");
  const snapshotParties = await partiesRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotParties.empty) {
    console.log("No matching parties.");
  } else {
    const partyUpdatePromises = [];
    snapshotParties.forEach((doc) => {
      const partyRef = db.collection("parties").doc(doc.id);
      partyUpdatePromises.push(partyRef.update({ tcId: null }));
    });
    await Promise.all(partyUpdatePromises);
    console.log(`Removed tcId from ${partyUpdatePromises.length} parties`);
  }

  console.log(`Successfully removed tcId from all documents, tasks, and parties for transaction ${transactionId}`);
  return;
}

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////  REMOVE TCID FROM TRANSACTION  ////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.removeAgentAsTcFromTransaction = functions.https.onCall(
  async (data, context) => {
    return new Promise(async function (resolve, reject) {
      const party = data.party;
      const transactionRef = db
        .collection("transactions")
        .doc(party.transactionId);
      await transactionRef.update({
        agentAsTcId: null,
        hasAgentAsTc: false,
        agentAsTransactionCoordinator: null,
      });
      await removeAgentAsTcFromDocsTasksParties(party.transactionId);
      resolve("Agent as TC removed");
    });
  }
);

exports.removeTcIdFromTransaction = functions.https.onCall(
  async (data, context) => {
    return new Promise(async function (resolve, reject) {
      const party = data.party;
      const transactionRef = db
        .collection("transactions")
        .doc(party.transactionId);
      await transactionRef.update({
        tcId: null,
        hasTc: false,
        transactionCoordinator: null,
      });
      await removeTcIdToDocsTasksParties(party.transactionId);
      resolve("TcId removed");
    });
  }
);

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////  ADD COAGENTID TO DOCS, TASKS, AND PARTIES  ///////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function addCoAgentIdToDocsTasksParties(transactionId, coAgentId) {
  const tasksRef = db.collection("tasks");
  const snapshotTask = await tasksRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotTask.empty) {
    console.log("No matching tasks.");
  } else {
    const taskUpdatePromises = [];
    snapshotTask.forEach((doc) => {
      const taskRef = db.collection("tasks").doc(doc.id);
      taskUpdatePromises.push(taskRef.update({ coAgentId: coAgentId }));
    });
    await Promise.all(taskUpdatePromises);
    console.log(`Updated ${taskUpdatePromises.length} tasks with coAgentId: ${coAgentId}`);
  }

  const docsRef = db.collection("documents");
  const snapshotDocs = await docsRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotDocs.empty) {
    console.log("No matching documents.");
  } else {
    const docUpdatePromises = [];
    snapshotDocs.forEach((doc) => {
      const docRef = db.collection("documents").doc(doc.id);
      docUpdatePromises.push(docRef.update({ coAgentId: coAgentId }));
    });
    await Promise.all(docUpdatePromises);
    console.log(`Updated ${docUpdatePromises.length} documents with coAgentId: ${coAgentId}`);
  }

  const partiesRef = db.collection("parties");
  const snapshotParties = await partiesRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotParties.empty) {
    console.log("No matching parties.");
  } else {
    const partyUpdatePromises = [];
    snapshotParties.forEach((doc) => {
      const partyRef = db.collection("parties").doc(doc.id);
      partyUpdatePromises.push(partyRef.update({ coAgentId: coAgentId }));
    });
    await Promise.all(partyUpdatePromises);
    console.log(`Updated ${partyUpdatePromises.length} parties with coAgentId: ${coAgentId}`);
  }

  console.log(`Successfully added coAgentId ${coAgentId} to all documents, tasks, and parties for transaction ${transactionId}`);
  return;
}

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////  ADD COAGENTID TO TRANSACTION  //////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.addCoAgentIdToTransaction = functions.https.onCall(
  async (data, context) => {
    return new Promise(async function (resolve, reject) {
      const party = data.party;
      // See if user already exists on our site
      auth
        .getUserByEmail(party.email)
        .then(async (userRecord) => {
          console.log("FOUND USER");
          const transactionRef = db
            .collection("transactions")
            .doc(party.transactionId);
          await transactionRef.update({
            coAgentId: userRecord.uid,
            hasCoAgent: true,
            coAgent: {
              email: party.email || "",
              firstName: party.firstName || "",
              lastName: party.lastName || "",
              userId: userRecord.uid,
            },
          });
          console.log("UPDATED TRANSACTION");
          await addCoAgentIdToDocsTasksParties(
            party.transactionId,
            userRecord.uid
          );
          console.log("TASK PARTIES");
          resolve(userRecord.uid);
        })
        .catch((error) => {
          console.log("CREATING NEW USER");
          getAuth()
            .createUser({
              email: party.email,
              password: party.email,
            })
            .then(async (userRecord) => {
              console.log("Successfully created new user:", userRecord.uid);
              const transactionRef = db
                .collection("transactions")
                .doc(party.transactionId);
              await transactionRef.update({
                coAgentId: userRecord.uid,
                hasCoAgent: true,
                coAgent: {
                  email: party.email || "",
                  firstName: party.firstName || "",
                  lastName: party.lastName || "",
                  userId: userRecord.uid,
                },
              });
              await db
                .collection("users")
                .doc(userRecord.uid)
                .set({
                  firstName: party.firstName || "First",
                  lastName: party.lastName || "Last",
                  email: party.email,
                  id: userRecord.uid,
                  userId: userRecord.uid,
                  role: "coagent",
                  type: "user",
                  createdAt: FieldValue.serverTimestamp(),
                });
              await addCoAgentIdToDocsTasksParties(
                party.transactionId,
                userRecord.uid
              );
              resolve(userRecord.uid);
            })
            .catch((error) => {
              console.log("Error creating new user:", error);
              reject("Error creating new user:", error);
            });
        });
    });
  }
);

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////  REMOVE COAGENTID FROM DOCS, TASKS, AND PARTIES  ////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function removeCoAgentIdToDocsTasksParties(transactionId) {
  const tasksRef = db.collection("tasks");
  const snapshotTask = await tasksRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotTask.empty) {
    console.log("No matching tasks.");
  } else {
    const taskUpdatePromises = [];
    snapshotTask.forEach((doc) => {
      const taskRef = db.collection("tasks").doc(doc.id);
      taskUpdatePromises.push(taskRef.update({ coAgentId: null }));
    });
    await Promise.all(taskUpdatePromises);
    console.log(`Removed coAgentId from ${taskUpdatePromises.length} tasks`);
  }

  const docsRef = db.collection("documents");
  const snapshotDocs = await docsRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotDocs.empty) {
    console.log("No matching documents.");
  } else {
    const docUpdatePromises = [];
    snapshotDocs.forEach((doc) => {
      const docRef = db.collection("documents").doc(doc.id);
      docUpdatePromises.push(docRef.update({ coAgentId: null }));
    });
    await Promise.all(docUpdatePromises);
    console.log(`Removed coAgentId from ${docUpdatePromises.length} documents`);
  }

  const partiesRef = db.collection("parties");
  const snapshotParties = await partiesRef
    .where("transactionId", "==", transactionId)
    .get();
  if (snapshotParties.empty) {
    console.log("No matching parties.");
  } else {
    const partyUpdatePromises = [];
    snapshotParties.forEach((doc) => {
      const partyRef = db.collection("parties").doc(doc.id);
      partyUpdatePromises.push(partyRef.update({ coAgentId: null }));
    });
    await Promise.all(partyUpdatePromises);
    console.log(`Removed coAgentId from ${partyUpdatePromises.length} parties`);
  }

  console.log(`Successfully removed coAgentId from all documents, tasks, and parties for transaction ${transactionId}`);
  return;
}

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////  REMOVE COAGENTID FROM TRANSACTION  //////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.removeCoAgentIdFromTransaction = functions.https.onCall(
  async (data, context) => {
    return new Promise(async function (resolve, reject) {
      const party = data.party;
      const transactionRef = db
        .collection("transactions")
        .doc(party.transactionId);
      await transactionRef.update({
        coAgentId: null,
        hasCoAgent: false,
        coAgent: null,
      });
      await removeCoAgentIdToDocsTasksParties(party.transactionId);
      resolve("CoAgentId removed");
    });
  }
);

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////  SHARE DOCUMENT WITH AGENT  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.shareWithAgent = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    let receiverTcId = "";
    let receiverAgentAsTcId = "";
    let receiverManagerId = "";
    let receiverCoAgentId = "";
    let docExtension = data.doc.filetype ? data.doc.filetype : "pdf";
    const receiverTransaction = await db
      .collection("transactions")
      .doc(data.party.linkedTransactionId)
      .get();
    if (receiverTransaction.exists) {
      receiverTcId = receiverTransaction.data().tcId || "";
      receiverAgentAsTcId = receiverTransaction.data().agentAsTcId || "";
      receiverManagerId = receiverTransaction.data().managerId || "";
      receiverCoAgentId = receiverTransaction.data().coAgentId || "";
    }
    const newDocId = db.collection("documents").doc().id;
    const srcFilePath = `users/${data.doc.userId}/${data.doc.transactionId}/${data.doc.id}/${data.doc.title}.${docExtension}`;
    const newDoc = data.doc;
    let newFormFieldValues = data.doc.formFieldValues || {};
    // Need to convert seriazlized date object fields to firestore timestamp
    Object.entries(newFormFieldValues).forEach(([key, value]) => {
      if (newFormFieldValues[key] && newFormFieldValues[key].seconds) {
        newFormFieldValues[key] = new Timestamp(
          newFormFieldValues[key].seconds,
          newFormFieldValues[key].nanoseconds
        ).toDate();
      }
    });
    let newFormFieldValuesForCopy = data.doc.formFieldValuesForCopy || {};
    // Need to convert seriazlized date object fields to firestore timestamp
    Object.entries(newFormFieldValuesForCopy).forEach(([key, value]) => {
      if (
        newFormFieldValuesForCopy[key] &&
        newFormFieldValuesForCopy[key].seconds
      ) {
        newFormFieldValuesForCopy[key] = new Timestamp(
          newFormFieldValuesForCopy[key].seconds,
          newFormFieldValuesForCopy[key].nanoseconds
        ).toDate();
      }
    });
    let newAnnotsToSign = data.doc.annotsToSign || {};
    // Need to convert seriazlized date object fields to firestore timestamp
    Object.entries(newAnnotsToSign).forEach(([key, value]) => {
      if (value?.signedAt?.seconds) {
        newAnnotsToSign[key]["signedAt"] = new Timestamp(
          newAnnotsToSign[key]["signedAt"].seconds,
          newAnnotsToSign[key]["signedAt"].nanoseconds
        ).toDate();
      }
    });
    let newAnnotsByAgent = data.doc.annotsByAgent || {};
    // Need to convert seriazlized date object fields to firestore timestamp
    Object.entries(newAnnotsByAgent).forEach(([key, value]) => {
      if (value?.signedAt?.seconds) {
        newAnnotsByAgent[key]["signedAt"] = new Timestamp(
          newAnnotsByAgent[key]["signedAt"].seconds,
          newAnnotsByAgent[key]["signedAt"].nanoseconds
        ).toDate();
      }
    });
    const destFilePath = `users/${data.party.linkedUserId}/${data.party.linkedTransactionId}/${newDocId}/${data.doc.title}.${docExtension}`;
    if (!newDoc.pdfBurnVersion || !newDoc.isStateForm) {
      newDoc.annotsInProgress = [];
      newDoc.annotsInProgressForCopy = [];
      newDoc.isStateForm = false;
      newDoc.docRef = destFilePath;
    }
    newDoc.annotsInProgressSuggestedAdded = false;
    newDoc.formFieldValues = newFormFieldValues;
    newDoc.formFieldValuesForCopy = newFormFieldValuesForCopy;
    newDoc.annotsToSign = newAnnotsToSign;
    newDoc.annotsByAgent = newAnnotsByAgent;
    newDoc.hasAnnotsToSign = false;
    newDoc.id = newDocId;
    newDoc.selectedSignerInProgress = {};
    newDoc.shared = true;
    newDoc.sharedFromAnotherAgent = true;
    newDoc.sharedBy = data.sharedBy;
    newDoc.sharingWith = [];
    newDoc.sharingWithRole = {};
    newDoc.signingDueAt = "";
    newDoc.signingEmailReminders = "";
    newDoc.signerListInProgress = false;
    newDoc.signingRequestedFor = false;
    newDoc.signingSentOutAt = "";
    newDoc.signed = false;
    newDoc.status = "In Progress";
    newDoc.subStatus = "Newly Shared";
    newDoc.transactionId = data.party.linkedTransactionId;
    newDoc.userId = data.party.linkedUserId;
    newDoc.tcId = receiverTcId;
    newDoc.agentAsTcId = receiverAgentAsTcId;
    newDoc.managerId = receiverManagerId;
    newDoc.coAgentId = receiverCoAgentId;
    newDoc.userEmail = "";
    newDoc.userFullname = "";
    newDoc.userFirstName = "";
    newDoc.userLastName = "";
    try {
      await db
        .collection("documents")
        .doc(newDocId)
        .set({
          ...newDoc,
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        });
      if (!newDoc.pdfBurnVersion || !newDoc.isStateForm) {
        const sourceFile = getStorage().bucket().file(srcFilePath);
        const destFile = getStorage().bucket().file(destFilePath);
        resolve(await sourceFile.copy(destFile));
      } else {
        resolve("success");
      }
    } catch (error) {
      console.log("ERROR: ", error);
      resolve(error);
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////////  SHARE TASK WITH AGENT  /////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.shareTaskWithAgent = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    let receiverTcId = "";
    let receiverAgentAsTcId = "";
    let receiverManagerId = "";
    let receiverCoAgentId = "";
    const receiverTransaction = await db
      .collection("transactions")
      .doc(data.party.linkedTransactionId)
      .get();
    if (receiverTransaction.exists) {
      receiverTcId = receiverTransaction.data().tcId || "";
      receiverAgentAsTcId = receiverTransaction.data().agentAsTcId || "";
      receiverManagerId = receiverTransaction.data().managerId || "";
      receiverCoAgentId = receiverTransaction.data().coAgentId || "";
    }
    const newTaskEndDate = new Date(data.task.end);
    const newTaskId = db.collection("tasks").doc().id;
    const newTask = data.task;
    newTask.end = newTaskEndDate;
    newTask.id = newTaskId;
    newTask.shared = true;
    newTask.sharedFromAnotherAgent = true;
    newTask.sharedBy = data.sharedBy;
    newTask.sharingWith = [];
    newTask.sharingWithRole = {};
    newTask.transactionId = data.party.linkedTransactionId;
    newTask.userId = data.party.linkedUserId;
    newTask.tcId = receiverTcId;
    newTask.agentAsTcId = receiverAgentAsTcId;
    newTask.managerId = receiverManagerId;
    newTask.coAgentId = receiverCoAgentId;
    try {
      await db
        .collection("tasks")
        .doc(newTaskId)
        .set({
          ...newTask,
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        });
      resolve("success");
    } catch (error) {
      console.log("ERROR: ", error);
      resolve(error);
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////////  LINK TRANSACTION WITH AGENT  ///////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.linkTransactionWithAgent = functions.https.onCall((data, context) => {
  return new Promise(async function (resolve, reject) {
    const party = data.party;
    const mls0 = data.values?.mlsNumbers?.[0];
    const mls1 = data.values?.mlsNumbers?.[1];
    const legalDescription = data.values?.propertyDetails?.legalDescription;
    const mlsNumbers = [];
    if (mls0) {
      mlsNumbers.push(mls0);
    }
    if (mls1) {
      mlsNumbers.push(mls1);
    }
    console.log("PARTY LINKEDUSERID: ", party.linkedUserId);
    if (!party.linkedUserId) {
      console.log("PARTY MISSING LINKED USERID");
      resolve({ error: "Party missing linked userId" });
    }
    let linkedTrans = null;
    let linkedTransId = "";
    if (legalDescription) {
      console.log("CHECKING FOR MATCHING LEGAL DESCRIPTION");
      linkedTrans = await db
        .collection("transactions")
        .where("userId", "==", party.linkedUserId)
        .where("propertyDetails.legalDescription", "==", legalDescription)
        // .limit(1)
        .get();
      if (!linkedTrans.empty) {
        linkedTrans.forEach((doc) => {
          // For dual agent don't match with current transaction
          if (doc.data().id !== party.transactionId) {
            linkedTransId = doc.data().id;
          }
        });
      }
    }
    if (!linkedTransId && mlsNumbers.length !== 0) {
      console.log("CHECKING FOR MATCHING MLS0 && MLS1");
      linkedTrans = await db
        .collection("transactions")
        .where("userId", "==", party.linkedUserId)
        .where("mlsNumbers", "array-contains-any", mlsNumbers)
        // .limit(1)
        .get();
      if (!linkedTrans.empty) {
        linkedTrans.forEach((doc) => {
          // For dual agent don't match with current transaction
          if (doc.data().id !== party.transactionId) {
            linkedTransId = doc.data().id;
          }
        });
      }
    }
    if (linkedTransId) {
      console.log("FOUND MATCHING TRANSACTION: ", linkedTransId);
      resolve({ transactionId: linkedTransId });
    } else {
      console.log("NO MATCHING TRANSACTION");
      resolve({
        error:
          "No matching transaction found. Please check the mls numbers or legal description and try again.",
      });
    }
  });
});

const ROLE_TYPE_VALUES = {
  ADMIN: "d",
  TC: "t",
  ASSISTANT: "a",
  MANAGING_BROKER: "m",
  MANAGER_ASSISTANT: "g", // New role for Manager Assistant
};
const ROLE_DESCRIPTIONS = {
  [ROLE_TYPE_VALUES.ADMIN]: "Admin",
  [ROLE_TYPE_VALUES.TC]: "TC",
  [ROLE_TYPE_VALUES.ASSISTANT]: "Assistant",
  [ROLE_TYPE_VALUES.MANAGING_BROKER]: "Managing Broker",
  [ROLE_TYPE_VALUES.MANAGER_ASSISTANT]: "Manager Assistant", // Description for new role
};

function getRole(role) {
  return ROLE_DESCRIPTIONS[role];
}

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////  ADD AUTH CLAIMS ASSISTANT  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function grantAuthClaimAssistant(
  assistantEmail,
  agentAssistingEmail,
  assistRole
) {
  console.log(
    "index::grantAuthClaimAssistant:: emails: ",
    assistantEmail,
    agentAssistingEmail
  );
  const user = await auth.getUserByEmail(assistantEmail);
  const agentUser = await auth.getUserByEmail(agentAssistingEmail);
  console.log("grantAssis::USER:: ", user);
  console.log("grantAssis::AGENTUSER:: ", agentUser);
  if (!user || !agentUser) {
    console.log("Bad user data came to grantAuthAss::", user, agentUser);
    return {
      error: "Bad Data. Try again.",
    };
  }
  let newActingAs = [agentUser.uid];
  if (
    user.customClaims &&
    user.customClaims?.a &&
    !user.customClaims?.a.includes(agentUser.uid)
  ) {
    newActingAs = [...user.customClaims.a, ...newActingAs];
  }
  console.log(
    `CURRENT ASSISTANTING FOR ${user.uid} IS ${user.customClaims?.a} SETTING TO ${newActingAs}`
  );
  db.collection("users")
    .doc(user.uid)
    .update({
      authCustomClaims: {
        r: assistRole,
        a: newActingAs,
      },
    });
  return auth.setCustomUserClaims(user.uid, {
    r: assistRole,
    a: newActingAs,
  });
}

exports.addAuthClaimAssistant = functions.https.onCall((data, context) => {
  console.log("index::addAuthClaimAss:: ADD ASSIST CONTEXT: ", context.auth);
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    return {
      error: "Request not authorized.",
    };
  }
  const assistantEmail = data.email;
  const agentAssistingEmail = data.agentAssistingEmail;
  const assistRole = data.role;
  console.log(
    "index::quick check role: ",
    assistRole,
    " emails: ",
    assistantEmail,
    agentAssistingEmail
  );
  return grantAuthClaimAssistant(
    assistantEmail,
    agentAssistingEmail,
    assistRole
  ).then(() => {
    return {
      result: `${assistantEmail} is now an ${assistRole} for ${agentAssistingEmail}`,
    };
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/////////////////  DELETE AUTH CLAIMS ASSISTANT  /////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function delAuthClaimAssistant(assistantEmail, agentAssistingEmail) {
  console.log(
    "index::delAssisting:: emails: ",
    assistantEmail,
    agentAssistingEmail
  );
  const user = await auth.getUserByEmail(assistantEmail);
  const agentUser = await auth.getUserByEmail(agentAssistingEmail);
  console.log("USER:: ", user);
  console.log("AGENTUSER:: ", agentUser);
  if (!user || !agentUser) {
    console.log("Delete Assisst::Bad User Data:: ", user, agentUser);
    return {
      error: "Bad Data. Try again.",
    };
  }
  let newActingAs = user.customClaims?.a;
  if (newActingAs?.includes(agentUser.uid)) {
    newActingAs = newActingAs.filter((id) => id !== agentUser.uid);
    console.log(
      "index::delAssisting::Removed list of who they assist: ",
      newActingAs
    );
    db.collection("users")
      .doc(user.uid)
      .update({
        authCustomClaims: {
          r: ROLE_TYPE_VALUES.ASSISTANT,
          a: newActingAs,
        },
      });
    return auth.setCustomUserClaims(user.uid, {
      r: ROLE_TYPE_VALUES.ASSISTANT,
      a: newActingAs,
    });
  } else {
    console.log("User is not an assistant or tc for that agent.");
    return {
      error: "User is not an assistant or tc for that agent.",
    };
  }
}

exports.delAuthClaimAssistant = functions.https.onCall((data, context) => {
  console.log("index::delAss:: DEL CONTEXT: ", context.auth);
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    console.log(
      "User not authorized to delete an assistant Email: ",
      context.auth.email
    );
    return {
      error: "Request not authorized.",
    };
  }
  const assistantEmail = data.email;
  const agentAssistingEmail = data.agentAssistingEmail;
  console.log(
    "index::quick check emails: ",
    assistantEmail,
    agentAssistingEmail
  );
  return delAuthClaimAssistant(assistantEmail, agentAssistingEmail).then(() => {
    return {
      result: `${assistantEmail} is no longer assisting for ${agentAssistingEmail}`,
    };
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/////////////////////  VIEW AUTH CLAIMS ROLE  ////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

const getEmailsFromIdList = (items) => {
  const emailList = [];
  return Promise.all(
    items.map(async (aid) => {
      const aemail = await auth.getUser(aid);
      emailList.push(aemail.email);
    })
  ).then(() => {
    return emailList;
  });
};

async function viewAuthClaimUserDetails(email) {
  try {
    const user = await auth.getUserByEmail(email);
    const userRole = user.customClaims?.r;
    const roleDesc = userRole ? getRole(userRole) : "";
    console.log(
      `index::VIEW::CURRENT ROLE FOR ${user.uid} [${user.email}] IS ${roleDesc}`
    );
    
    let resultString = `${email} role: ${roleDesc}`;
    
    // Show agents they assist
    if (user.customClaims?.a) {
      const assistingEmails = await getEmailsFromIdList(user.customClaims.a);
      resultString += ` ${assistingEmails}`;
    }
    
    // Show managers they assist
    if (user.customClaims?.m) {
      const managersEmails = await getEmailsFromIdList(user.customClaims.m);
      resultString += ` Assisting Managers: ${managersEmails}`;
    }
    
    return { result: resultString };
  } catch (error) {
    return {
      error: "Error user not found.",
    };
  }
}

exports.viewAuthClaimRole = functions.https.onCall((data, context) => {
  console.log(
    "index::VIEW:: role: ",
    context.auth.token.r,
    ROLE_TYPE_VALUES.ADMIN
  );
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    console.log(
      "index::VIEW:: User not authorized. Email: ",
      context.auth.email
    );
    return {
      error: "Request not authorized.",
    };
  }
  const email = data.email;
  return viewAuthClaimUserDetails(email);
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/////////////////////  ADD AUTH CLAIMS ROLE  /////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function grantAuthClaimRole(email, role) {
  const user = await auth.getUserByEmail(email);
  console.log(`CURRENT ROLE FOR ${user.uid} IS ${user.customClaims?.r}`);
  console.log(`SETTTING AUTH ROLE FOR ${user.uid} TO ${role}`);
  if (user.customClaims && user.customClaims?.r === role) {
    return;
  }
  db.collection("users")
    .doc(user.uid)
    .update({
      authCustomClaims: {
        r: role,
      },
    });
  return auth.setCustomUserClaims(user.uid, {
    r: role,
  });
}

exports.addAuthClaimRole = functions.https.onCall((data, context) => {
  console.log("index::addRole:: CONTEXT: ", context.auth);
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN ) {
  	console.log("index::exports.addAuth:: role is not admin - access denied")
  	return {
  		error: "Request not authorized."
  	};
  };
  const email = data.email;
  const role = data.role;
  const roleDesc = getRole(role);
  console.log("index::addAuthClaim::roleDesc: ", role, roleDesc);
  return grantAuthClaimRole(email, role).then(() => {
    return {
      result: `${email} now has the role: ${roleDesc}`,
    };
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////  VIEW ALL USERS WITH AUTH CLAIMS  /////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function getAllUsersWithCustomClaims() {
  try {
    const snapshotUsers = await db
      .collection("users")
      .where("authCustomClaims", "!=", null)
      .get();
    if (snapshotUsers.empty) {
      console.log("No matching documents.");
    } else {
      return snapshotUsers.docs.map((doc) => doc.data());
    }
  } catch (error) {
    return {
      error: "Error user not found.",
    };
  }
}

exports.viewAllUsersWithCustomClaims = functions.https.onCall(
  (data, context) => {
    if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
      return {
        error: "Request not authorized.",
      };
    }
    return getAllUsersWithCustomClaims();
  }
);

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////  LOGIN CLIENT  ////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.loginClient = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    const partyRef = db.collection("parties").doc(data.partyId);
    const party = await partyRef.get();
    if (!party.exists) {
      reject("URL does not match our records.");
    } else if (party.data().email !== data.email) {
      reject("Email does not match our records.");
    } else {
      auth
        .getUserByEmail(party.data().email)
        .then((userRecord) => {
          const returnData = { ...party.data(), currentUser: true };
          resolve(returnData);
        })
        .catch((error) => {
          const returnData = { ...party.data(), currentUser: false };
          resolve(returnData);
        });
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/////////////////////////  LOGIN PARTY  //////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.partyLogin = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    console.log("DATA: ", data);
    auth
      .getUserByEmail(data)
      .then((userRecord) => {
        const returnData = { isUser: true };
        resolve(returnData);
      })
      .catch((error) => {
        const returnData = { isUser: false };
        resolve(returnData);
      });
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////  ADMIN CHANGE USER EMAIL  /////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function updateEmailForUser(emailOld, emailNew) {
  try {
    const auth = getAuth();
    const user = await getAuth().getUserByEmail(emailOld);
    if (!user) {
      return { error: "ERROR: User not found.", };
    }
    console.log(`index::Updating email ${user.uid} [${user.email}] to ${emailNew}`);
    // console.log(user);

    const tokenResult = await auth.createCustomToken(user.uid);
    console.log("UPDATE EMAIL::TOKEN: ", tokenResult);
    if (!tokenResult) {
      return {
        error: "Error user not found.",
      };
    }

    const result = getAuth().updateUser(user.uid, { email: emailNew }).then(async (userRecord) => {
      console.log("UPDATE EMAIL FOR USER::: Successfully updated user's email in Auth", userRecord.uid);
      console.log("UPDATE EMAIL FOR USER::: Now update users");
      // NEED to update: users.email, transactions.sharingWith[array] and .sharingWithRole[map role:email]
      await db.collection("users")    // this action takes 4 minutes on local
        .doc(user.uid)
        .update({
            email: emailNew,

        });
      console.log("UPDATE EMAIL FOR USER::: Just updated users, now for xact");

      let myTrans = await db
        .collection("transactions")
        .where("agentProfile.email", "==", emailOld)
        .get()
        .then(async (myTransList) => {
          if ( myTransList && !myTransList.empty) {
            console.log("FOUND MY TRANSACTIONS:: ");
            // console.log("number = ", myTransList?.length);
          
            myTransList?.forEach((doc) => {
              console.log("this doc xact id: ", doc.id);
              // if (doc?.agentProfile) {
              //   console.log("agentProfile = ", doc?.agentProfile);
              //   console.log("     and current email = ", doc?.agentProfile?.email);
              // }
              // else console.log("agentProfile DNE");

              // const partyRef = db.collection("parties").doc(data.partyId);
              const docRef = db.collection("transactions").doc(doc.id);
             docRef.update({
              [`agentProfile.email`]: emailNew,
            });

            });
            } else console.log("No transactions found where agentProfile.email = ", emailOld);
        });
        
      console.log("ended my transactions. Next do shared transactions");

      let sharedTrans = await db
        .collection("transactions")
        .where("sharingWith", "array-contains", emailOld)
        .get().then((snapshot) => {
          snapshot.forEach((doc) => {
            console.log("FOUND SHARED TRANSACTIONS:: ");
          
            const docRef = db.collection("transactions").doc(doc.id);
            docRef.update({
              sharingWith: FieldValue.arrayUnion(emailNew)
            });

            docRef.update({
              sharingWith: FieldValue.arrayRemove(emailOld)
            });
            
            console.log("added and deleted sharingWith array");
        
          });
        });

    

    }).catch((error) => {
      console.log("ERROR UPDATE EMAIL FOR USER:: ", error);
    });
    console.log("after try");
   
    // const tokenResult = await auth.createCustomToken(user.uid);
    // console.log(`index::Updating email token = ${tokenResult}`);

    //    const result = updateEmail(user, emailNew).then(() => {
  //   const result = auth.updateUser(uid, ({
  //        email: "<EMAIL>"
  //        })
  //     => {
  //     // Email updated! Now go through hard-coded areas
  //     // ...
  //     console.log("UPDATE EMAIL: success in system")
  //   }).catch((error) => {
  //     // error
  //     console.log("UPDATE EMAIL: error in system");

  //   });
    return { result };
  } catch (error) {
      console.log("ERROR: bottom catch on update user email ", error);
    return {
      error: "Error user not found.",
    };
  }

}

exports.updateUserEmail = functions.https.onCall((data, context) => {
  console.log(
    "index::UPDATEUSEREMAIL:: role: ",
    context.auth.token.r,
    ROLE_TYPE_VALUES.ADMIN
  );
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    console.log(
      "index::UPDATE USER EMAIL:: User not authorized. Email: ",
      context.auth.email
    );
    return {
      error: "Update user email: Request not authorized.",
    };
  }
  const emailOld = data.email;
  const emailNew = data.emailNew;
  return updateEmailForUser(emailOld, emailNew);
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////////////  IMPERSONATE USER  //////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function impersonate(email) {
  try {
    const user = await auth.getUserByEmail(email);
    console.log(`index::IMPERSONATING ${user.uid} [${user.email}]`);
    const tokenResult = await auth.createCustomToken(user.uid);
    console.log("TOKEN: ", tokenResult);
    return { tokenResult };
  } catch (error) {
    console.log("ERROR impers: ", error);
    return {
      error: "Error user not found.",
    };
  }
}

exports.impersonateUser = functions.https.onCall((data, context) => {
  console.log(
    "index::VIEW:: role: ",
    context.auth.token.r,
    ROLE_TYPE_VALUES.ADMIN
  );
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    console.log(
      "index::VIEW:: User not authorized. Email: ",
      context.auth.email
    );
    return {
      error: "Request not authorized.",
    };
  }
  const email = data.email;
  return impersonate(email);
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////////  FETCH LEGAL ADDRESS FROM PRECISELY  /////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

function convertAddressFull(address) {
  console.log("inside ConvertAddressFull, address coming in = ", address);
  let addressFull = "";
  if (address?.street) {
    addressFull = addressFull + address.street;
  }
  // PRECISELY:: #2 works, '2' does not work, APT 2 works, UNIT 2 works, 'apt apt 2' works
  if (address?.unit) {
    addressFull = addressFull + " APT " + address.unit;
  }
  if (address?.city) {
    addressFull = addressFull + " " + address.city;
  }
  if (address?.state) {
    addressFull = addressFull + ", " + address.state;
  }
  if (address?.zipcode) {
    addressFull = addressFull + " " + address.zipcode;
  }
  return addressFull;
}

// CURL WORKS FROM COMMAND LINE
// curl -H "Content-Type: application/x-www-form-urlencoded"
// -H "Authorization: Basic xxxxxxxxxxxx"
// -X POST "https://api.precisely.com/oauth/token"
// -d "grant_type=client_credentials"
// 2	    "access_token": "ISSUED_ACCESS_TOKEN",
// 3	    "token_type": "Bearer",
// 4	    "refresh_token": "ISSUED_REFRESH_TOKEN",
// 5	    "expires_in": 28800

async function getPreciselyAuthAccessToken() {
  let result = { access_token: "", error: "" };

  const apiToken = process.env.FUNCTIONS_PRECISELY_API_KEY_TOKEN;
  const secretToken = process.env.FUNCTIONS_PRECISELY_SECRET_TOKEN;
  const plainCreds = `${apiToken}:${secretToken}`;
  const bearerToken = Buffer.from(plainCreds, "utf8").toString("base64");
  const base64x = "Basic " + bearerToken;

  const axios = require("axios");
  let dataApi = "grant_type=client_credentials";

  let config = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://api.precisely.com/oauth/token",
    headers: {
      Authorization: base64x,
    },
    data: dataApi,
  };

  const response = await axios.request(config);

  if (!response) {
    console.log("COULD NOT GET A TOKEN:: config = ", config);
    return result;
  }
  console.log("response is : ", response.data);
  const access_token = response.data.access_token;
  result.access_token = access_token;
  return result;
}

exports.fetchLegalAddressData = functions.https.onCall(
  async (data, context) => {
    console.log("Fetch legal address function start");
    return new Promise(async function (resolve, reject) {
      getPreciselyAuthAccessToken()
        .then((response) => {
          console.log("did response wait for await? : ", response);
          const access_token = response.access_token;

          if (response.error) {
            console.log("getLegalAddr: error: ", response.error);
            reject("Error accessing public data: ", response.error);
          }
          if (!access_token) {
            console.log("getLegalAddr: no access token.");
            reject("Unable to get access to public data.");
          }

          const apiUrl = "https://api.precisely.com/";
          const convertedAddress = convertAddressFull(data);
          console.log("Converted address:: ", convertedAddress);
          if (!convertedAddress) {
            reject("No address to process: " + convertedAddress);
          }

          const authAxios = axios.create({
            baseUrl: apiUrl,
            headers: {
              Authorization: `Bearer ${access_token}`,
            },
          });

          const callUrl = `${apiUrl}/property/v2/attributes/byaddress?address=${convertedAddress}&attributes=legalAssessorDesc`;
          console.log("calling this url:: ", callUrl);
          authAxios
            .get(
              `${apiUrl}/property/v2/attributes/byaddress?address=${convertedAddress}&attributes=legalAssessorDesc`
            )
            .then((legalDescResult) => {
              if (legalDescResult) {
                console.log(
                  "Precisely::legalDesc = ",
                  legalDescResult.data.propertyAttributes.legalAssessorDesc
                );
                resolve(
                  legalDescResult.data.propertyAttributes.legalAssessorDesc
                );
              } else {
                console.log(
                  "Precisely Problem, no result for legal description."
                );
                reject("No properties found with that address.");
              }
            })
            .catch((error) => {
              console.log("Error on call to legal desc:: error = ", error);
              reject(error);
            });
        })
        .catch((error) => {
          console.log("Yet another problem in life: ", error);
          reject(error);
        });
    });
  }
);

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////////  FETCH OWNER DATA FROM PRECISELY  ////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.fetchOwnerData = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    getPreciselyAuthAccessToken().then((response) => {
      console.log("did response wait for await? : ", response);
      const access_token = response.access_token;

      if (!access_token) {
        reject("Error no access token.");
      }
      if (response.error) {
        reject("Error accessing token: " + response.error);
      }

      const apiUrl = "https://api.precisely.com/";
      const authAxios = axios.create({
        baseUrl: apiUrl,
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });

      const convertedAddress = convertAddressFull(data);
      console.log("Converted address:: ", convertedAddress);

      authAxios
        .get(
          `${apiUrl}/property/v2/attributes/byaddress?address=${convertedAddress}&attributes=owners`
        )
        .then((result) => {
          if (result) {
            console.log(
              "We got owners:::",
              result.data.propertyAttributes.owners
            );
            resolve(result.data.propertyAttributes.owners);
          } else {
            reject("No properties found with that address.");
          }
        });
    });
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
////////////////  FETCH MLS DATA FROM DATAFINITI  ////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.fetchMlsDataFiniti = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    const accessToken = process.env.FUNCTIONS_DATAFINITI_MLS_ACCESS_TOKEN;
    const apiUrl = "https://api.datafiniti.co/v4";
    const mlsId = data.mlsId;
    const state = data.state;
    const cities = (data.cities ? " AND city:(" + data.cities + ")" : "");
    const authAxios = axios.create({
      baseUrl: apiUrl,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    const queryString =
      "country:US AND province:" + state + " AND mlsNumber:" + mlsId + cities;
    try {
      const result = await authAxios.post(`${apiUrl}/properties/search`, {
        view: "main",
        query: queryString,
        format: "JSON",
        num_records: 1,
        download: false,
      });
      if (result?.data?.records?.[0]) {
        const values = result.data.records[0];
        const parcelNumber =
          values["features"].find(({ key }) => key === "APN")?.value?.[0] ||
          null;
        const formattedData = {
          address: {
            street: values["address"] || null,
            city: values["city"] || null,
            state: values["province"] || null,
            zipcode: values["postalCode"] || null,
          },
          propertyDetails: {
            county: values["county"] || null,
            parcelNumber: parcelNumber,
            subdivisionName: values["subdivision"] || null,
            yearBuilt: values["yearBuilt"] || null,
          },
          pic: values["imageURLs"]?.[0] || "",
          listingAgent: {
            firstName:
              values["mostRecentBrokerAgent"]?.split(" ")?.[0] ||
              values["people"]?.[0]?.["name"]?.split(" ")?.[0] ||
              null,
            lastName:
              values["mostRecentBrokerAgent"]?.split(" ")?.pop() ||
              values["people"]?.[0]?.["name"]?.split(" ")?.pop() ||
              null,
            email:
              values["mostRecentBrokerEmails"]?.[0] ||
              values["people"]?.[0]?.["email"] ||
              values["people"]?.[1]?.["email"] ||
              null,
            phone: values["mostRecentBrokerPhones"]?.[0] || null,
            brokerageName: values["mostRecentBrokerCompany"] || null,
          },
        };
        resolve(formattedData);
      } else {
        resolve(false);
        // reject("No properties found with that MLS ID");
      }
    } catch (error) {
      resolve(false);
      // reject(error);
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////////////  GET VIEWS FROM DATAFINITI  //////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.fetchViewsFromDataFiniti = functions.https.onCall(
  async (data, context) => {
    return new Promise(async function (resolve, reject) {
      const accessToken = process.env.FUNCTIONS_DATAFINITI_MLS_ACCESS_TOKEN;
      const apiUrl = "https://api.datafiniti.co/v4";

      const authAxios = axios.create({
        baseUrl: apiUrl,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      try {
        const result = await authAxios.get(`${apiUrl}/views`);
        // To delete a view
        // const result = await authAxios.delete(`${apiUrl}/views/test3`);
        console.log("RETURNED DATA: ", result?.data);
        if (result?.data) {
          const returnData = result.data;
          resolve(returnData);
        } else {
          reject("No views found");
        }
      } catch (error) {
        reject(error);
      }
    });
  }
);

exports.uploadPdfFromUrl = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    try {
      // Validate user authentication
      if (!context.auth) {
        reject({ error: "Authentication required" });
        return;
      }

      const { url, filename, transactionId, userId } = data;

      // Validate required parameters
      if (!url || !filename || !transactionId || !userId) {
        reject({ error: "Missing required parameters: url, filename, transactionId, userId" });
        return;
      }

      // Validate URL format (basic check)
      if (!url.includes('ctmecontracts.com')) {
        reject({ error: "Invalid URL: Only CTMecontracts.com URLs are supported" });
        return;
      }

      console.log("Downloading PDF from URL:", url);

      // Download the PDF from the external URL
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        timeout: 30000, // 30 second timeout
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      // Validate response
      if (!response.data || response.status !== 200) {
        reject({ error: "Failed to download PDF from URL" });
        return;
      }

      // Validate content type
      const contentType = response.headers['content-type'];
      if (!contentType || !contentType.includes('pdf')) {
        console.warn("Content type is not PDF:", contentType);
      }

      // Generate document ID and storage path
      const docId = db.collection("documents").doc().id;
      const cleanFilename = filename.replace(/[^\w\s\-_.()]/g, '').trim();
      const docPath = `users/${userId}/${transactionId}/${docId}/${cleanFilename}`;

      console.log("Uploading to storage path:", docPath);

      // Upload to Firebase Storage - Use simpler approach to avoid signed URL issues
      const bucket = getStorage().bucket();
      const file = bucket.file(docPath);

      await file.save(Buffer.from(response.data), {
        metadata: {
          contentType: 'application/pdf',
          metadata: {
            originalUrl: url,
            uploadedAt: new Date().toISOString(),
            uploadedBy: context.auth.uid
          }
        }
      });

      // For now, return the storage path - the frontend will handle getting the download URL
      // This avoids the signed URL authentication issue
      console.log("PDF uploaded successfully:", docPath);

      resolve({
        success: true,
        docId: docId,
        docPath: docPath,
        downloadUrl: `gs://${bucket.name}/${docPath}`, // Return storage path
        filename: cleanFilename,
        fileSize: response.data.byteLength
      });

    } catch (error) {
      console.error("Error uploading PDF from URL:", error);

      // Provide more specific error messages
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        reject({ error: "Unable to connect to the PDF source. Please check the URL." });
      } else if (error.code === 'ETIMEDOUT') {
        reject({ error: "Request timed out. The PDF source may be slow or unavailable." });
      } else if (error.response && error.response.status === 404) {
        reject({ error: "PDF not found at the provided URL." });
      } else if (error.response && error.response.status === 403) {
        reject({ error: "Access denied to the PDF URL." });
      } else {
        reject({ error: error.message || "Failed to upload PDF from URL" });
      }
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////////////  CREATE VIEW IN DATAFINITI  //////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.createViewInDataFiniti = functions.https.onCall(
  async (data, context) => {
    return new Promise(async function (resolve, reject) {
      const accessToken = process.env.FUNCTIONS_DATAFINITI_MLS_ACCESS_TOKEN;
      const apiUrl = "https://api.datafiniti.co/v4";

      const authAxios = axios.create({
        baseUrl: apiUrl,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      try {
        const result = await authAxios.post(`${apiUrl}/views`, {
          name: "main",
          data_type: "property",
          fields: [
            { name: "address" },
            { name: "city" },
            { name: "province" },
            { name: "postalCode" },
            { name: "county" },
            { name: "subdivision" },
            { name: "yearBuilt" },
            { name: "mostRecentBrokerAgent" },
            { name: "mostRecentBrokerEmails" },
            { name: "mostRecentBrokerPhones" },
            { name: "mostRecentBrokerCompany" },
            { name: "imageURLs" },
            { name: "people", flatten: false },
            { name: "features", flatten: false },
          ],
        });
        console.log("RETURNED DATA: ", result?.data);
        if (result?.data) {
          const returnData = result.data;
          resolve(returnData);
        } else {
          reject("Error creating view");
        }
      } catch (error) {
        reject(error);
      }
    });
  }
);

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////////////  FETCH MLS DATA FROM IRES  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.fetchMlsIres = functions.https.onCall(async (data, context) => {
  console.log("fetch IRES");
  return new Promise(async function (resolve, reject) {
    const accessToken = process.env.FUNCTIONS_IRES_MLS_ACCESS_TOKEN;
    const apiUrl = "https://api.bridgedataoutput.com/api/v2/OData/iresds";

    const authAxios = axios.create({
      baseUrl: apiUrl,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    try {
      const result = await authAxios.get(
        `${apiUrl}/Property?$filter=ListingId eq '${data}'&$expand=ListAgent&$expand=ListOffice`
      );
      if (result?.data?.value?.[0]) {
        const values = result.data.value[0];
        const formattedData = {
          address: {
            street:
              values["StreetNumber"] +
              " " +
              values["StreetName"] +
              " " +
              values["StreetSuffix"],
            unit: values["UnitNumber"],
            city: values["City"],
            state: values["StateOrProvince"],
            zipcode: values["PostalCode"],
          },
          propertyDetails: {
            county: values["CountyOrParish"],
            legalDescription: values["TaxLegalDescription"],
            parcelNumber: values["ParcelNumber"],
            subdivisionName: values["SubdivisionName"],
            yearBuilt: values["YearBuilt"],
            privateRemarks: values["PrivateRemarks"],
            inclusions: values["Inclusions"],
            exclusions: values["Exclusions"],
          },
          pic: values["Media"]?.[0]?.["MediaURL"],
          listingAgent: {
            firstName: values["ListAgent"]?.["MemberFirstName"],
            lastName: values["ListAgent"]?.["MemberLastName"],
            email: values["ListAgentEmail"],
            phone: values["ListAgentPreferredPhone"],
            address: {
              street: values["ListAgent"]?.["MemberAddress1"],
              city: values["ListAgent"]?.["MemberCity"],
              state: values["ListAgent"]?.["MemberStateOrProvince"],
              zipcode: values["ListAgent"]?.["MemberPostalCode"],
            },
            brokerLicenseNumber: values["ListAgent"]?.["MemberStateLicense"],
            colistingAgent: values["CoListAgentFullName"],
            brokerageName: values["ListOffice"]?.["OfficeName"],
            brokerageId: values["ListAgent"]?.["OfficeMlsId"],
          },
        };
        resolve(formattedData);
      } else {
        resolve(false);
        // reject("No properties found with that MLS ID");
      }
    } catch (error) {
      resolve(false);
      // reject(error);
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
///////////////////  FETCH MLS DATA FROM CREN  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
 
    // In Node.js:
    //   Axios does not automatically manage cookies in Node.js as it uses the http module instead of XMLHttpRequest.
    //   To handle session cookies in Node.js, you typically need to use a cookie management library in conjunction with Axios,
    //     such as axios-cookiejar-support and tough-cookie.
    
// const cors = require('cors')({ origin: true });

exports.fetchMlsCoCren = functions.https.onCall(async (data, context) => {

  // Testing curl --user 'enter:enter' https://cren-rets.paragonrels.com/rets/fnisrets.aspx/CREN/login?rets-version=rets/1.7.2
  // GetMetadata=/rets/fnisrets.aspx/CREN/getmetadata?rets-version=rets/1.7.2
  // GetObject=/rets/fnisrets.aspx/CREN/getobject?rets-version=rets/1.7.2
  // Login=https://cren-rets.paragonrels.com/rets/fnisrets.aspx/CREN/login?rets-version=rets/1.7.2
  // Logout=/rets/fnisrets.aspx/CREN/logout?rets-version=rets/1.7.2
  // Search=/rets/fnisrets.aspx/CREN/search?rets-version=rets/1.7.2
  // MED_media_url
  // COMPACT-DECODED translates numbers for appliance inclusions to their names like stove,microwave
  // Note that most MLS RETS instructions say not to use XML (and it relies on StandardNames which is incomplete)
    

  return new Promise(async function (resolve, reject) {

    console.log("fetch CREN with MLS Id: ", data);

    // Import required packages
    const axios = require("axios");
    const tough = require('tough-cookie');
    const { wrapper } = await import('axios-cookiejar-support');  // dynamic import

    const apiBaseUrl = "https://cren-rets.paragonrels.com/rets/fnisrets.aspx/CREN";
    
    // CREDS
    const username = process.env.FUNCTIONS_COCREN_MLS_ACCESS_ID;
    const password = process.env.FUNCTIONS_COCREN_MLS_ACCESS_PWD;

    // Create cookie jar and axios instance with cookie support
    const jar = new tough.CookieJar();
    const client = wrapper(axios.create({
      jar,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'RETS-Version': "RETS/1.7.2",
        'User-Agent': 'transactioner.com/1.0'
      }
    }));
    
    // Set up Basic Auth
    client.defaults.auth = {
      username: username,
      password: password
    };
    
    try {
      // Step 1: Login to get session
      console.log("Logging in to CREN RETS...");
      const loginResponse = await client.get(`${apiBaseUrl}/login?rets-version=rets/1.7.2`);
      console.log("Login successful");
      
      // Step 2: Perform search with the authenticated client
      const mlsId = data; // The MLS ID passed to the function
      const searchQueryParams = `&SearchType=Property&Class=RE_1&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,L_StatusCatID,L_Address,L_AskingPrice,LM_char512_1,LM_char100_2,L_AddressNumber,L_AddressDirection,L_AddressStreet,L_Address2,L_City,L_State,L_Zip,LFD_INTERIORINCLUSIONS_15,L_ListAgent1,L_ListAgent2,L_ListOffice1,LO1_BranchOfOrgID,LO1_OrgCity,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_Char25_18,LM_char10_47,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_PhoneNumber1,LA1_Email,L_ListOffice1,L_DisplayId,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_char10_47,LM_char30_5,LM_char100_2,LFD_APPLIANCEINCLUSIONS_11,LA1_UserFirstName,LA1_UserLastName,LA1_State,LA1_Zip,LA1_PhoneNumber1,LA1_Email,LO1_OrganizationName,LO1_OrgCity,L_ListAgent1,LA2_UserFirstName,LA2_UserLastName,LA2_PhoneNumber1,LA2_Email&Query=(ListingId=${mlsId})&Limit=1`;
      console.log("Performing search for MLS ID:", mlsId);
      let retsClass = "RE_1";
      const searchResponse = await client.get(`${apiBaseUrl}/search?rets-version=rets/1.7.2${searchQueryParams}`);      
      // console.log("Search results: ", searchResponse.data);

      // Parse the COMPACT format response
      const xml2js = require('xml2js');
      const parser = new xml2js.Parser({ explicitArray: false });
      const result = await parser.parseStringPromise(searchResponse.data);
      let retsData = result.RETS;
      // console.log("Parsed XML:", JSON.stringify(result, null, 2));

      if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
        // try Class=FR_5 FarmRanch
        retsClass = "FR_5";
        const searchQueryParamsFarm = `&SearchType=Property&Class=FR_5&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,L_StatusCatID,L_Address,L_AskingPrice,LM_char512_1,LM_char100_2,L_AddressNumber,L_AddressDirection,L_AddressStreet,L_Address2,L_City,L_State,L_Zip,L_ListAgent1,L_ListAgent2,L_ListOffice1,LO1_BranchOfOrgID,LO1_OrgCity,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_Char25_18,LM_char10_47,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_PhoneNumber1,LA1_Email,L_ListOffice1,L_DisplayId,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_char10_47,LM_char30_5,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_State,LA1_Zip,LA1_PhoneNumber1,LA1_Email,LO1_OrganizationName,LO1_OrgCity,L_ListAgent1,LA2_UserFirstName,LA2_UserLastName,LA2_PhoneNumber1,LA2_Email&Query=(ListingId=${mlsId})&Limit=1`;
        // console.log("Performing search for MLS ID as Farm/Ranch:", mlsId);
        const searchResponse = await client.get(`${apiBaseUrl}/search?rets-version=rets/1.7.2${searchQueryParamsFarm}`);        
        // console.log("Search results: ", searchResponse.data);
        // Parse the COMPACT format response
        const parser = new xml2js.Parser({ explicitArray: false });
        const result = await parser.parseStringPromise(searchResponse.data);
        retsData = result.RETS;

        if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
          // try LD_2 Lots and Land
          retsClass = "LD_2";
          const searchQueryParamsLand = `&SearchType=Property&Class=LD_2&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,L_StatusCatID,L_Address,L_AskingPrice,LM_char512_1,LM_char100_2,L_AddressNumber,L_AddressDirection,L_AddressStreet,L_City,L_State,L_Zip,L_ListAgent1,L_ListAgent2,L_ListOffice1,LO1_BranchOfOrgID,LO1_OrgCity,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_Char25_18,LM_char10_47,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_PhoneNumber1,LA1_Email,L_ListOffice1,L_DisplayId,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_char10_47,LM_char30_5,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_State,LA1_Zip,LA1_PhoneNumber1,LA1_Email,LO1_OrganizationName,LO1_OrgCity,L_ListAgent1,LA2_UserFirstName,LA2_UserLastName,LA2_PhoneNumber1,LA2_Email&Query=(ListingId=${mlsId})&Limit=1`;
          // console.log("Performing search for MLS ID as Land:", mlsId);
          const searchResponse = await client.get(`${apiBaseUrl}/search?rets-version=rets/1.7.2${searchQueryParamsLand}`);        
          // console.log("Search results: ", searchResponse.data);

          // Parse the COMPACT format response
          // const xml2js = require('xml2js');
          const parser = new xml2js.Parser({ explicitArray: false });
          const result = await parser.parseStringPromise(searchResponse.data);
          retsData = result.RETS;
          if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
            // try MF_4 Multifamily
            retsClass = "MF_4";
            const searchQueryParamsMultifam = `&SearchType=Property&Class=MF_4&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,L_StatusCatID,L_Address,L_AskingPrice,LM_char512_1,LM_char100_2,L_AddressNumber,L_AddressDirection,L_AddressStreet,L_Address2,L_City,L_State,L_Zip,L_ListAgent1,L_ListAgent2,L_ListOffice1,LO1_BranchOfOrgID,LO1_OrgCity,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_Char25_18,LM_char10_47,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_PhoneNumber1,LA1_Email,L_ListOffice1,L_DisplayId,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_char10_47,LM_char30_5,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_State,LA1_Zip,LA1_PhoneNumber1,LA1_Email,LO1_OrganizationName,LO1_OrgCity,L_ListAgent1,LA2_UserFirstName,LA2_UserLastName,LA2_PhoneNumber1,LA2_Email&Query=(ListingId=${mlsId})&Limit=1`;
            // console.log("Performing search for MLS ID as MultiFamily:", mlsId);
            const searchResponse = await client.get(`${apiBaseUrl}/search?rets-version=rets/1.7.2${searchQueryParamsMultifam}`);        
            // console.log("Search results: ", searchResponse.data);
  
            // Parse the COMPACT format response
            // const xml2js = require('xml2js');
            const parser = new xml2js.Parser({ explicitArray: false });
            const result = await parser.parseStringPromise(searchResponse.data);
            retsData = result.RETS;
              if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
                // try CI_3 Commercial
                retsClass = "CI_3";
                const searchQueryParamsCom = `&SearchType=Property&Class=CI_3&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,L_StatusCatID,L_Address,L_AskingPrice,LM_char512_1,LM_char100_2,L_AddressNumber,L_AddressDirection,L_AddressStreet,L_Address2,L_City,L_State,L_Zip,L_ListAgent1,L_ListAgent2,L_ListOffice1,LO1_BranchOfOrgID,LO1_OrgCity,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_Char25_18,LM_char10_47,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_PhoneNumber1,LA1_Email,L_ListOffice1,L_DisplayId,LM_Char10_11,LM_Char10_12,LM_Char25_15,LM_Char25_16,LM_char10_47,LM_char30_5,LM_char100_2,LA1_UserFirstName,LA1_UserLastName,LA1_State,LA1_Zip,LA1_PhoneNumber1,LA1_Email,LO1_OrganizationName,LO1_OrgCity,L_ListAgent1,LA2_UserFirstName,LA2_UserLastName,LA2_PhoneNumber1,LA2_Email&Query=(ListingId=${mlsId})&Limit=1`;
                // console.log("Performing search for MLS ID as Commercial:", mlsId);
                const searchResponse = await client.get(`${apiBaseUrl}/search?rets-version=rets/1.7.2${searchQueryParamsCom}`);        
                // console.log("Search results: ", searchResponse.data);
      
                // Parse the COMPACT format response
                // const xml2js = require('xml2js');
                const parser = new xml2js.Parser({ explicitArray: false });
                const result = await parser.parseStringPromise(searchResponse.data);
                retsData = result.RETS;
                if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
                  // try TF_& timeshare fractional
                  resolve({ success: false, error: "Invalid Listing # for CREN MLS." });
                }
              }
          }
        }
      }
      const extractedData = {};
      const extractedDataPhoto = {};

      if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
        resolve({ success: false, error: "Invalid Listing # for CREN MLS." });
      }
      else {
        // Extract column names and data from COMPACT format
        const columns = retsData.COLUMNS;
        const columnNames = columns.split('\t');
        const dataRows = retsData.DATA;
        // Create an object with column names as keys
        const values = dataRows.split('\t');
        for (let i = 0; i < columnNames.length; i++) {
          extractedData[columnNames[i]] = values[i] || null;
        }

        // Step 2b: Photo
        // https://cren-rets.paragonrels.com/rets/fnisrets.aspx/CREN//getObject?rets-version=rets/1.7.2&Resource=Property&Class=RE_1&Type=Photo&Location=0&Id=824176
        // the link below works 
        // https://cren-rets.paragonrels.com/rets/fnisrets.aspx/CREN//getObject?rets-version=rets/1.7.2&Resource=Property&Class=RE_1&Type=Photo&Location=1&Id=824176
        // PHOTO URLS::: https://cren-rets.paragonrels.com/rets/fnisrets.aspx/CREN//search?rets-version=rets/1.7.2&Resource=Property&Class=RE_1&SearchType=Media&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,MED_media_url&Query=(ListingId=824377)&Limit=1
        const photoQueryParams = `&Resource=Property&Class=RE_1&SearchType=Media&QueryType=DMQL2&Format=COMPACT-DECODED&StandardNames=0&Select=L_ListingID,MED_media_url&Query=(ListingId=${mlsId})&Limit=1`;
        const photoResponse = await client.get(`${apiBaseUrl}/search?rets-version=rets/1.7.2${photoQueryParams}`);
      
        // console.log("Photo results: ", photoResponse.data);
        // const xml2js = require('xml2js');
        const parserPhoto = new xml2js.Parser({ explicitArray: false });
        const resultPhoto = await parserPhoto.parseStringPromise(photoResponse.data);
        const photoData = resultPhoto.RETS;

        if (!photoData || !photoData.COLUMNS || !photoData.DATA) {
          // ignore photo errors, proceed with extractedData
        } else {

          const columnsPhoto = photoData.COLUMNS;
          const columnNamesPhoto = columnsPhoto.split('\t');
          const dataRowsPhoto = photoData.DATA;
          let photoUrl = null;
          if (dataRowsPhoto?.length > 0) {
            // Create an object with column names as keys
            const valuesPhoto = dataRowsPhoto.split('\t');
            for (let i = 0; i < columnNamesPhoto.length; i++) {
              extractedDataPhoto[columnNamesPhoto[i]] = valuesPhoto[i] || null;
            }
          }
        }
      }

      // Step 3: Logout to clean up session
      await client.get(`${apiBaseUrl}/logout?rets-version=rets/1.7.2`);
      // console.log("Logged out of RETS");

      if (!retsData || !retsData.COLUMNS || !retsData.DATA) {
        resolve(new Error("Invalid MLS # for CREN MLS. Please check the MLS ID and try again."));
      }

      // const streetNumber = extractedData.L_AddressNumber || '';
      // const streetDirection = extractedData.L_AddressDirection ? ' ' + extractedData.L_AddressDirection : '';
      // const streetName = ' ' + extractedData.L_AddressStreet || '';

      // Format the data using the extracted column-based data
      const formattedData = {
        address: {
          street: extractedData.L_Address || null,
          // street: streetNumber
          //   + streetDirection
          //   + streetName,
          unit: extractedData.L_Address2 || null, // Not available in current select fields
          city: extractedData.L_City || null,
          state: extractedData.L_State || 'CO', // Default to Colorado since it's CREN
          zipcode: extractedData.L_Zip || null,
        },
        propertyDetails: {
          county: extractedData.LM_char10_47 || null,
          legalDescription: extractedData.LM_char512_1 || null, // Not available in current select fields
          parcelNumber: null, // Not available in current select fields
          subdivisionName: null, // Not available in current select fields
          yearBuilt: null, // Not available in current select fields
          privateRemarks: extractedData.LM_char100_2 || null,
          inclusions: extractedData.LFD_APPLIANCEINCLUSIONS_11 || null,
          exclusions: null, // Not available in current select fields
          associationFee: extractedData.LM_Char10_11 || null, // Association Fee
          associationFeeFreq: extractedData.LM_Char10_12 || null,
          metroDistrictWebsite: extractedData.LM_char100_2 || null,
        },
        pic: extractedDataPhoto?.MED_media_url ? 'https:' + extractedDataPhoto.MED_media_url : null, // You'll need to make a separate request for photos
        listingAgent: {
          firstName: extractedData.LA1_UserFirstName || null,
          lastName: extractedData.LA1_UserLastName || null,
          email: extractedData.LA1_Email || null,
          phone: extractedData.LA1_PhoneNumber1 || null,
          address: {
            street: '', // Not available in current select fields
            city: extractedData.LO1_OrgCity || null, // Not available in current select fields
            state: extractedData.LA1_State || null,
            zipcode: extractedData.LA1_Zip || null,
          },
          brokerLicenseNumber: '', // Not available in current select fields
          colistingAgent: (extractedData.LA2_UserLastName ? extractedData.LA2_UserFirstName + ' ' + extractedData.LA2_UserLastName : null), 
          brokerageName: extractedData.LO1_OrganizationName || null,
          brokerageId: null, // Not available in current select fields
        },
        listPrice: extractedData.L_AskingPrice || null,
        listingStatus: null, // Not available in current select fields
        mlsNumber: extractedData.L_ListingID || extractedData.L_DisplayId || data,
        // Additional fields from the COMPACT format
        // rawData: extractedData, // Include all raw data for access by column name
        customFields: {
          emDepositAmount: extractedData.LM_Char25_15 || null,
          emHolder: extractedData.LM_Char25_16 || null,
          titleCo: extractedData.LM_Char25_18 || null,
          ownerName: extractedData.LM_char30_5 || null,
        }
      };

      // console.log("Formatted data:", formattedData);
      resolve(formattedData);

    } catch (error) {
      console.error("RETS API Error:", error.message);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
      }
      resolve({ success: false, error: error.message });
    }
  });
});

   



//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/////////////////////////  DELETE TRANSACTION  ///////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

exports.deleteTransaction = functions.https.onCall(async (data, context) => {
  return new Promise(async function (resolve, reject) {
    try {
      const transactionId = data;
      const batch = db.batch();
      const tasksRef = db.collection("tasks");
      const snapshotTask = await tasksRef
        .where("transactionId", "==", transactionId)
        .get();
      if (!snapshotTask.empty) {
        snapshotTask.forEach((doc) => {
          batch.delete(db.collection("tasks").doc(doc.id));
        });
      }
      const docsRef = db.collection("documents");
      const snapshotDocs = await docsRef
        .where("transactionId", "==", transactionId)
        .get();
      if (!snapshotDocs.empty) {
        snapshotDocs.forEach((doc) => {
          batch.delete(db.collection("documents").doc(doc.id));
        });
      }
      const partiesRef = db.collection("parties");
      const snapshotParties = await partiesRef
        .where("transactionId", "==", transactionId)
        .get();
      if (!snapshotParties.empty) {
        snapshotParties.forEach((doc) => {
          batch.delete(db.collection("parties").doc(doc.id));
        });
      }
      batch.delete(db.collection("transactions").doc(transactionId));
      await batch.commit();
      resolve("Success");
    } catch (error) {
      reject(error);
    }
  });
});

//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////  ADD AUTH CLAIMS MANAGER ASSISTANT  ///////////////////
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

async function grantAuthClaimManagerAssistant(
  managerAssistantEmail,
  managerEmail,
  assistRole
) {
  console.log(
    "index::grantAuthClaimManagerAssistant:: emails: ",
    managerAssistantEmail,
    managerEmail
  );
  const user = await auth.getUserByEmail(managerAssistantEmail);
  const managerUser = await auth.getUserByEmail(managerEmail);
  console.log("grantManagerAssis::USER:: ", user);
  console.log("grantManagerAssis::MANAGERUSER:: ", managerUser);
  if (!user || !managerUser) {
    console.log("Bad user data came to grantAuthManagerAss::", user, managerUser);
    return {
      error: "Bad Data. Try again.",
    };
  }
  let newAssistingManagers = [managerUser.uid];
  if (
    user.customClaims &&
    user.customClaims?.m &&
    !user.customClaims?.m.includes(managerUser.uid)
  ) {
    newAssistingManagers = [...user.customClaims.m, ...newAssistingManagers];
  }
  console.log(
    `CURRENT MANAGER ASSISTANT FOR ${user.uid} IS ${user.customClaims?.m} SETTING TO ${newAssistingManagers}`
  );
  db.collection("users")
    .doc(user.uid)
    .update({
      authCustomClaims: {
        r: assistRole,
        m: newAssistingManagers,
      },
    });
  return auth.setCustomUserClaims(user.uid, {
    r: assistRole,
    m: newAssistingManagers,
  });
}

exports.addAuthClaimManagerAssistant = functions.https.onCall((data, context) => {
  console.log("index::addAuthClaimManagerAss:: ADD MANAGER ASSIST CONTEXT: ", context.auth);
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    return {
      error: "Request not authorized.",
    };
  }
  const managerAssistantEmail = data.email;
  const managerEmail = data.managerEmail;
  const assistRole = data.role;
  const title = data.title || "Manager Assistant";

  console.log(
    "index::quick check role: ",
    assistRole,
    " emails: ",
    managerAssistantEmail,
    managerEmail, " title: ", title
  );

  return grantAuthClaimManagerAssistant(
    managerAssistantEmail,
    managerEmail,
    assistRole,
    title
  ).then(() => {
    return {
      result: `${managerAssistantEmail} is now a ${assistRole} (${title}) for ${managerEmail}`,
    };
  });
});

async function delAuthClaimManagerAssistant(managerAssistantEmail, managerEmail) {
  console.log(
    "index::delManagerAssisting:: emails: ",
    managerAssistantEmail,
    managerEmail
  );
  const user = await auth.getUserByEmail(managerAssistantEmail);
  const managerUser = await auth.getUserByEmail(managerEmail);
  console.log("USER:: ", user);
  console.log("MANAGERUSER:: ", managerUser);
  if (!user || !managerUser) {
    console.log("Delete Manager Assisst::Bad User Data:: ", user, managerUser);
    return {
      error: "Bad Data. Try again.",
    };
  }
  let newAssistingManagers = user.customClaims?.m;
  if (newAssistingManagers?.includes(managerUser.uid)) {
    newAssistingManagers = newAssistingManagers.filter((id) => id !== managerUser.uid);
    console.log(
      "index::delManagerAssisting::Removed list of managers they assist: ",
      newAssistingManagers
    );
    db.collection("users")
      .doc(user.uid)
      .update({
        authCustomClaims: {
          r: ROLE_TYPE_VALUES.MANAGER_ASSISTANT,
          m: newAssistingManagers,
        },
      });
    return auth.setCustomUserClaims(user.uid, {
      r: ROLE_TYPE_VALUES.MANAGER_ASSISTANT,
      m: newAssistingManagers,
    });
  } else {
    console.log("User is not a manager assistant for that manager.");
    return {
      error: "User is not a manager assistant for that manager.",
    };
  }
}

exports.delAuthClaimManagerAssistant = functions.https.onCall((data, context) => {
  console.log("index::delManagerAss:: DEL CONTEXT: ", context.auth);
  if (context.auth.token.r !== ROLE_TYPE_VALUES.ADMIN) {
    console.log(
      "User not authorized to delete a manager assistant Email: ",
      context.auth.email
    );
    return {
      error: "Request not authorized.",
    };
  }
  const managerAssistantEmail = data.email;
  const managerEmail = data.managerEmail;
  console.log(
    "index::quick check emails: ",
    managerAssistantEmail,
    managerEmail
  );
  return delAuthClaimManagerAssistant(managerAssistantEmail, managerEmail).then(() => {
    return {
      result: `${managerAssistantEmail} is no longer assisting for manager ${managerEmail}`,
    };
  });
});
